version: "3.9"

services:
# geodjango
  project:
    build:
        context: .
        dockerfile: ./Dockerfile_web.dev
    command: python3 manage.py runserver 0.0.0.0:8000
    volumes:
      - .:/app
    ports:
      - 8002:8000
    env_file:
      - .env
    restart: always
    depends_on:
      - db
    networks:
      - web_network
# database postgis
  db:
    image: postgis/postgis:14-3.3
    volumes:
        - postgres_data:/var/lib/postgresql/data
        # - ./postgres_data/data:/var/lib/postgresql/data
        # copy the sql script to create tables
        # - ./postgres/test.sql:/docker-entrypoint-initdb.d/init.sql
    environment:
        - POSTGRES_USER=${POSTGRES_USER}
        - POSTGRES_PASSWORD=${POSTGRES_PASS}
        - POSTGRES_DB=${POSTGRES_DBNAME}
    restart: always
    ports:
        - ${PG_PORT}:5432 # สำหรับเข้าผ่าน pgAdmin
    networks:
        - web_network
    expose:
        - ${PG_PORT_expose} # สำหรับเข้าผ่านภายใน network

# pgAdmin
  pgadmin:
    image: dpage/pgadmin4
    restart: always
    environment:
        - PGADMIN_DEFAULT_EMAIL=${PGADMIN_DEFAULT_EMAIL}
        - PGADMIN_DEFAULT_PASSWORD=${PGADMIN_DEFAULT_PASSWORD}
    ports:
        - ${PGADMIN_PORT}:80
    volumes:
        - ./pgadmin/data:/var/lib/pgadmin
    depends_on:
        - db
    networks:
        - web_network

networks:
  web_network:
    name: geodjango_web_network
    driver : bridge

volumes:
  postgres_data:
      